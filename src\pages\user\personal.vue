<template>
  <view class="personal-info-page">
    <!-- 个人信息列表 -->
    <view class="info-list">
      <!-- 头像 -->
      <view class="info-item avatar-item" @click="handleAvatarClick">
        <view class="info-label">头像</view>
        <view class="info-value avatar-section">
          <view class="avatar-container">
            <image class="avatar-img" :src="userInfo?.avatarUrl || '/static/logo.png'" mode="aspectFill"></image>
          </view>
          <view class="arrow-icon">
            <image class="arrow-img" src="@/static/public/chevron-right.png" mode="aspectFit"></image>
          </view>
        </view>
      </view>
      
      <!-- 工号 -->
      <view class="info-item">
        <view class="info-label">工号</view>
        <view class="info-value">{{ userInfo?.id || '' }}</view>
      </view>

      <!-- 账号 -->
      <view class="info-item">
        <view class="info-label">账号</view>
        <view class="info-value">{{ formatPhone(userInfo?.username) || '138****3456' }}</view>
      </view>

      <!-- 姓名 -->
      <view class="info-item">
        <view class="info-label">姓名</view>
        <view class="info-value">{{ userInfo?.realName || '王晓' }}</view>
      </view>

      <!-- 性别 -->
      <view class="info-item">
        <view class="info-label">性别</view>
        <view class="info-value">{{ getGenderText(userInfo?.gender) || '男' }}</view>
      </view>

      <!-- 工龄 -->
      <view class="info-item">
        <view class="info-label">工龄</view>
        <view class="info-value">{{ userInfo?.workYears || '5' }}</view>
      </view>

      <!-- 服务类型 -->
      <view class="info-item">
        <view class="info-label">服务类型</view>
        <view class="info-value">{{ userInfo?.serviceTypes || '就医陪诊、代办问诊' }}</view>
      </view>

      <!-- 状态 -->
      <view class="info-item">
        <view class="info-label">状态</view>
        <view class="info-value status-text" :class="getStatusClass(userInfo?.status)">
          {{ userInfo?.statusDesc || '正常' }}
        </view>
      </view>
    </view>

    <!-- 返回按钮 -->
    <view class="back-button" @click="goBack">
      <view class="back-icon">
        <image
            class="avatar-img"
            src="@/static/public/chevron-left.png"
            mode="scaleToFill"
        />
      </view>
      <text class="back-text">返回</text>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue';
import { useUserStore } from '@/store/modules/user';
import DictManager,{GENDER} from '@/utils/dict';
import OssClient from '@/utils/oss';
import { userApi } from '@/apis/user/userApi';
import { onShow } from '@dcloudio/uni-app';
const userStore = useUserStore();
const ossClient = new OssClient();
onShow(() => {
  console.log('onShow');
})

// 用户信息
const userInfo = computed(() => userStore.userInfo);
console.log('userInfo',userInfo);

// 格式化手机号
const formatPhone = (phone) => {
  if (!phone) return '';
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
};

// 获取性别文本
const getGenderText = (gender) => {
  return DictManager.getLabel(GENDER, gender) || '未知';
};


// 获取状态样式类
const getStatusClass = (status) => {
  const statusClasses = {
    1: 'status-normal',
    0: 'status-disabled',
    2: 'status-pending'
  };
  return statusClasses[status] || 'status-normal';
};

// 头像点击事件
const handleAvatarClick = () => {
  uni.showActionSheet({
    itemList: ['拍照', '从相册选择'],
    success: (res) => {
      if (res.tapIndex === 0) {
        chooseImage('camera');
      } else if (res.tapIndex === 1) {
        chooseImage('album');
      }
    }
  });
};

// 选择图片
const chooseImage = (sourceType) => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: [sourceType],
    success: (res) => {
      console.log('res',res);
      
      const tempFilePath = res.tempFilePaths[0];
      // TODO: 上传头像到服务器
      const fileType = res.tempFiles[0].name.substring(res.tempFiles[0].name.lastIndexOf('.') + 1);
      uploadAvatar(tempFilePath, fileType);
    },
    fail: (error) => {
      console.error('选择图片失败:', error);
      uni.showToast({
        title: '选择图片失败',
        icon: 'none'
      });
    }
  });
};

// 上传头像
const uploadAvatar = async (filePath,fileType ='jpg') => {
  uni.showLoading({
    title: '上传中...'
  });
  console.log('filePath',filePath);
  
  const url = await ossClient.upload(ossClient.randFileName(fileType),filePath);
  // 修改用户信息
  const urlPath = url.substring(url.indexOf('com') + 3);
  const res = await userApi.editUserInfo({avatarUrl:urlPath,id:userInfo.value.id});
  
  userStore.setUserInfo(res);
  console.log('上传成功:', url);

  uni.hideLoading();
  uni.showToast({
    title: '上传成功',
    icon: 'success' 
  });
  
};

// 返回上一页
const goBack = () => {
  uni.navigateBack({
    delta: 1
  });
};
</script>

<style lang="scss" scoped>
.personal-info-page {
  min-height: 100vh;
  background-color: #fff;
  position: relative;
}


// 信息列表
.info-list {
  margin-left: 32rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  
  .info-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx;
    padding-left: 0;
    border-bottom: 1rpx solid #f0f0f0;
    
    &.avatar-item {
      &:active {
        background-color: #f8f8f8;
      }
    }
    
    .info-label {
      font-size: 32rpx;
      color: $uni-text-color-black;
      font-weight: 400;
      min-width: 120rpx;
    }
    
    .info-value {
      font-size: 32rpx;
      color: rgba(0,0,0,0.9);
      flex: 1;
      text-align: right;
      
      &.status-text {
        font-weight: 500;
        
        &.status-normal {
          color: #52c41a;
        }
        
        &.status-disabled {
          color: #f5222d;
        }
        
        &.status-pending {
          color: #fa8c16;
        }
      }
    }
    
    .avatar-section {
      display: flex;
      align-items: center;
      justify-content: end;
      .avatar-container {
        width: 96rpx;
        height: 96rpx;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 28rpx;
        .avatar-img {
          width: 100%;
          height: 100%;
        }
      }
      
      .arrow-icon {
        width: 48rpx;
        height: 48rpx;
        
        .arrow-img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}

// 返回按钮
.back-button {
  position: fixed;
  bottom: 104rpx;
  right: 14rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 196rpx;
  height: 96rpx;
  background-color: $uni-color-third-primary;
  border-radius: 200rpx;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }

  .back-icon {
    width: 44rpx;
    height: 44rpx;
    .avatar-img {
      width: 100%;
      height: 100%;
    }
  }

  .back-text {
    margin-left: 8rpx;
    font-size: 32rpx;
    color: #fff;
    font-weight: 600;
  }
}

// 底部安全区域
.safe-area-bottom {
  height: constant(safe-area-inset-bottom);
  height: env(safe-area-inset-bottom);
}
</style>
