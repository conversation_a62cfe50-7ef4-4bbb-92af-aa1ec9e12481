<template>
  <view class="user-center-page">
    <!-- 用户信息卡片 -->
    <view class="user-info-card">
      <view class="user-avatar-section">
        <view class="avatar-container">
          <image class="avatar-img" :src="userInfo?.avatarUrl || '/static/logo.png'" mode="aspectFill"></image>
        </view>
        <view class="user-details">
          <view class="user-name">{{ userInfo?.realName || '王晓' }}</view>
          <view class="user-job">工号：{{ userInfo?.id || '10086' }}</view>
        </view>
      </view>
    </view>

    <!-- 功能菜单列表 -->
    <view class="menu-list">
      <view
        v-for="(item, index) in menuList"
        :key="index"
        class="menu-item"
        @click="handleMenuClick(item)"
      >
        <view class="menu-icon">
          <view class="icon-emoji">
            <image class="icon-img" :src="item.src" mode="aspectFit"></image>
          </view>
        </view>
        <view class="menu-title">{{ item.title }}</view>
        <view class="menu-arrow">
          <image class="arrow-icon" src="@/static/public/chevron-right.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 返回按钮 -->
    <view class="back-button" @click="goBack">
      <view class="back-icon">
        <image
            class="avatar-img"
            src="@/static/public/chevron-left.png"
            mode="scaleToFill"
        />
      </view>
      <text class="back-text">返回</text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useUserStore } from '@/store/modules/user';

const userStore = useUserStore();

// 用户信息
const userInfo = computed(() => userStore.userInfo);
console.log('userInfo',userInfo);

// 功能菜单列表
const menuList = ref([
  {
    id: 'personal',
    title: '个人信息',
    src: '/static/img/userCenter/user.png',
    path: '/pages/user/personal'
  },
  {
    id: 'service',
    title: '服务记录',
    src: '/static/img/userCenter/root-list.png',
    path: '/pages/user/service-record'
  },
  {
    id: 'password',
    title: '修改密码',
    src: '/static/img/userCenter/edit.png',
    path: '/pages/login/resetPwd'
  },
  {
    id: 'logout',
    title: '退出登录',
    src: '/static/img/userCenter/logout.png',
    path: ''
  }
]);

// 菜单点击事件
const handleMenuClick = (item) => {
  console.log('点击菜单:', item);

  if (item.id === 'logout') {
    handleLogout();
  } else if (item.path) {
    uni.navigateTo({
      url: item.path
    });
  }
};

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          await userStore.logout();
          uni.showToast({
            title: '退出成功',
            icon: 'success'
          });

          // 跳转到登录页
          setTimeout(() => {
            uni.reLaunch({
              url: '/pages/login/login'
            });
          }, 1500);
        } catch (error) {
          console.error('退出登录失败:', error);
          uni.showToast({
            title: '退出失败，请重试',
            icon: 'none'
          });
        }
      }
    }
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack({
    delta: 1
  });
};
</script>

<style lang="scss" scoped>
.user-center-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx 32rpx;
  position: relative;
}


// 用户信息卡片
.user-info-card {
  padding: 32rpx 32rpx;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

  .user-avatar-section {
    display: flex;
    align-items: center;

    .avatar-container {
      width: 128rpx;
      height: 128rpx;
      margin-right: 32rpx;
      border-radius: 128rpx;

      .avatar-img {
        width: 100%;
        height: 100%;
        border-radius: 128rpx;
      }
    }

    .user-details {
      flex: 1;

      .user-name {
        font-size: 36rpx;
        font-weight: 600;
        color: $uni-text-color-black;
        margin-bottom: 12rpx;
      }

      .user-job {
        border-radius: 6rpx;
        padding: 6rpx 16rpx;
        width: fit-content;
        font-size: 24rpx;
        color: $uni-text-color-black;
        background-color: $uni-bg-color-grey;
      }
    }
  }
}

// 功能菜单列表
.menu-list {
  margin-top: 32rpx;
  background-color: #fff;
  border-radius: 24rpx;

  .menu-item {
    display: flex;
    align-items: center;
    padding: 32rpx;
    border-bottom: 1rpx solid #f0f0f0;
    transition: background-color 0.3s ease;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: #f8f8f8;
    }

    .menu-icon {
      width: 64rpx;
      height: 64rpx;
      margin-right: 24rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .icon-emoji {
        width: 48rpx;
        height: 48rpx;
        .icon-img{
          width: 100%;
          height: 100%;
        }
      }
    }

    .menu-title {
      flex: 1;
      font-size: 32rpx;
      color: $uni-text-color-black;
      font-weight: 400;
    }

    .menu-arrow {
      width: 48rpx;
      height: 48rpx;

      .arrow-icon {
        width: 100%;
        height: 100%;
      }
    }
  }
}

// 返回按钮
.back-button {
  position: fixed;
  bottom: 104rpx;
  right: 14rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 196rpx;
  height: 96rpx;
  background-color: $uni-color-third-primary;
  border-radius: 200rpx;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }

  .back-icon {
    width: 44rpx;
    height: 44rpx;
    .avatar-img {
      width: 100%;
      height: 100%;
    }
  }

  .back-text {
    margin-left: 8rpx;
    font-size: 32rpx;
    color: #fff;
    font-weight: 600;
  }
}

// 底部安全区域
.safe-area-bottom {
  height: constant(safe-area-inset-bottom);
  height: env(safe-area-inset-bottom);
}
</style>
