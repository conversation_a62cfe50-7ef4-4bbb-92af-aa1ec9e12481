import request from '@/utils/request/uni-request'
const base = '/accompany/user'
// 用户信息相关Api
export const userApi = {
    /**
   * 编辑用户信息
   * @param {Object} data - 用户信息
   * @param {string} data.avatarUrl - 头像
   * @param {string} data.id - 用户id
   * @returns {Promise} 用户信息
   */
  editUserInfo(data) {
    return request.put(`${base}/edit`, data)
  },
  /**
   * 获取用户信息
   * @returns {Promise} 用户信息
   */
  getUserInfo() {
    return request.get(`${base}/query`)
  },
}