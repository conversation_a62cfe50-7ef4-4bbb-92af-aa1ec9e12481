import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import {secondsToTimestamp} from '@/utils/utils'
export const useUserStore = defineStore('user', () => {
  // 本地存储键名
  const ACCESS_TOKEN_KEY = 'access_token'
  const REFRESH_TOKEN_KEY = 'refresh_token'

  // 状态
  const userInfo = ref({})
  const isLoggedIn = ref(false)

  // Token相关状态
  const accessToken = ref('')
  const refreshToken = ref('')
  const accessTokenExpireTime = ref(0)
  const refreshTokenExpireTime = ref(0)

  // 计算属性
  const hasValidToken = computed(() => {
    return accessToken.value && accessTokenExpireTime.value > Date.now()
  })

  const hasValidRefreshToken = computed(() => {
    return refreshToken.value && refreshTokenExpireTime.value > Date.now()
  })

  const isAccessTokenExpired = computed(() => {
    return accessTokenExpireTime.value <= Date.now()
  })

  const isRefreshTokenExpired = computed(() => {
    return refreshTokenExpireTime.value <= Date.now()
  })

  const accessTokenRemainingTime = computed(() => {
    const remainingMs = accessTokenExpireTime.value - Date.now()
    return Math.max(0, Math.floor(remainingMs / 1000))
  })

  const refreshTokenRemainingTime = computed(() => {
    const remainingMs = refreshTokenExpireTime.value - Date.now()
    return Math.max(0, Math.floor(remainingMs / 1000))
  })

  const isAccessTokenExpiringSoon = computed(() => {
    const remainingTime = accessTokenRemainingTime.value
    return remainingTime > 0 && remainingTime <= 300 // 5分钟
  })

  // Token管理方法
  const setTokens = (newAccessToken, newRefreshToken, accessTokenExpiresIn, refreshTokenExpiresIn) => {
    // 处理 accessToken 过期时间
    const finalAccessExpireTime = typeof accessTokenExpiresIn === 'number' && accessTokenExpiresIn < Date.now()
      ? Date.now() + (accessTokenExpiresIn * 1000) - (5 * 60 * 1000) // 减去5分钟缓冲时间
      : accessTokenExpiresIn

    // 处理 refreshToken 过期时间
    const finalRefreshExpireTime = typeof refreshTokenExpiresIn === 'number' && refreshTokenExpiresIn < Date.now()
      ? Date.now() + (refreshTokenExpiresIn * 1000) - (5 * 60 * 1000) // 减去5分钟缓冲时间
      : refreshTokenExpiresIn

    accessToken.value = newAccessToken
    refreshToken.value = newRefreshToken
    accessTokenExpireTime.value = finalAccessExpireTime
    refreshTokenExpireTime.value = finalRefreshExpireTime

    // 同步到本地存储
    uni.setStorageSync(ACCESS_TOKEN_KEY, newAccessToken)
    uni.setStorageSync(REFRESH_TOKEN_KEY, newRefreshToken)
    uni.setStorageSync('access_token_expire_time', finalAccessExpireTime)
    uni.setStorageSync('refresh_token_expire_time', finalRefreshExpireTime)
  }

  const clearTokens = () => {
    accessToken.value = ''
    refreshToken.value = ''
    accessTokenExpireTime.value = 0
    refreshTokenExpireTime.value = 0

    // 清除本地存储
    uni.removeStorageSync(ACCESS_TOKEN_KEY)
    uni.removeStorageSync(REFRESH_TOKEN_KEY)
    uni.removeStorageSync('access_token_expire_time')
    uni.removeStorageSync('refresh_token_expire_time')
  }

  // 从本地存储恢复token
  const restoreTokensFromStorage = () => {
    const storedAccessToken = uni.getStorageSync(ACCESS_TOKEN_KEY)
    const storedRefreshToken = uni.getStorageSync(REFRESH_TOKEN_KEY)
    const storedAccessExpireTime = uni.getStorageSync('access_token_expire_time')
    const storedRefreshExpireTime = uni.getStorageSync('refresh_token_expire_time')

    if (storedAccessToken && storedRefreshToken) {
      accessToken.value = storedAccessToken
      refreshToken.value = storedRefreshToken
      accessTokenExpireTime.value = storedAccessExpireTime || 0
      refreshTokenExpireTime.value = storedRefreshExpireTime || 0
    }
  }

  // 验证token格式
  const validateTokenFormat = (token) => {
    if (!token || typeof token !== 'string') {
      return false
    }
    // 简单的JWT格式验证（三个部分用.分隔）
    const parts = token.split('.')
    return parts.length === 3
  }
  const setUserInfo = (userInfo) => {

      userInfo.value = userInfo
      uni.setStorageSync('userInfo', userInfo)
  }
  // 登录方法 - 现在只处理token设置，不调用API
  const login = (response) => {
    try {
      // 在store中直接处理token设置
      const { accessToken, refreshToken, accessTokenExpiresIn = 7200, refreshTokenExpiresIn = 86400 } = response
      setTokens(accessToken, refreshToken, accessTokenExpiresIn, refreshTokenExpiresIn)
      isLoggedIn.value = true

    } catch (error) {
      console.error('Login failed:', error)
      throw error
    }
  }

  const logout = () => {
    // 在store中直接处理状态清除
    userInfo.value = null
    isLoggedIn.value = false
    clearTokens()

    // 清除本地存储
    uni.removeStorageSync('userInfo')

    // 跳转到登录页
    uni.reLaunch({
      url: '/pages/login/login'
    })
  }

  const updateUserInfo = (newUserInfo) => {
    userInfo.value = { ...userInfo.value, ...newUserInfo }
    uni.setStorageSync('userInfo', userInfo.value)
  }

  // 处理认证错误
  const handleAuthError = () => {
    // 直接清除token值
    accessToken.value = ''
    refreshToken.value = ''
    accessTokenExpireTime.value = 0
    refreshTokenExpireTime.value = 0

    // 清除本地存储
    uni.removeStorageSync('access_token')
    uni.removeStorageSync('refresh_token')
    uni.removeStorageSync('access_token_expire_time')
    uni.removeStorageSync('refresh_token_expire_time')

    uni.showToast({
      title: '登录已过期，请重新登录',
      icon: 'none'
    })

    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/login/login'
      })
    }, 1500)
  }

  // 获取访问令牌
  const getAccessToken = () => {
    return accessToken.value || ''
  }

  // 获取刷新令牌
  const getRefreshToken = () => {
    return refreshToken.value || ''
  }

  const initUserState = () => {
    // 从本地存储恢复token
    restoreTokensFromStorage()

    // 从本地存储恢复用户信息
    const storedUserInfo = uni.getStorageSync('userInfo')
    if (storedUserInfo) {
      userInfo.value = storedUserInfo
    }

    // 检查token状态
    isLoggedIn.value = hasValidToken.value

    // 如果有用户信息但token无效，清除用户信息
    if (userInfo.value && !hasValidToken.value) {
      userInfo.value = null
      uni.removeStorageSync('userInfo')
    }
  }

  const checkAuthStatus = () => {
    const hasToken = hasValidToken.value

    if (!hasToken && isLoggedIn.value) {
      // token失效，清除登录状态
      logout()
    }

    return hasToken
  }

  return {
    // 状态
    userInfo,
    isLoggedIn,
    accessToken,
    refreshToken,
    accessTokenExpireTime,
    refreshTokenExpireTime,

    // 计算属性
    hasValidToken,
    hasValidRefreshToken,
    isAccessTokenExpired,
    isRefreshTokenExpired,
    accessTokenRemainingTime,
    refreshTokenRemainingTime,
    isAccessTokenExpiringSoon,

    // 动作
    login,
    logout,
    updateUserInfo,
    initUserState,
    checkAuthStatus,
    setUserInfo,

    // Token管理方法
    setTokens,
    clearTokens,
    restoreTokensFromStorage,
    validateTokenFormat,

    // 认证相关方法
    handleAuthError,
    getAccessToken,
    getRefreshToken
  }
}, {
  // 使用 pinia-plugin-unistorage 持久化部分状态
  unistorage: {
    paths: ['userInfo', 'isLoggedIn', 'accessToken', 'refreshToken', 'tokenExpireTime']
  }
})